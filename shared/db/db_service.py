import logging
from datetime import datetime, timedelta, timezone
from typing import List, Type

from . import models, database
from sqlalchemy.orm import Session, joinedload
from ..config import config
from .models import Alerts, Aggregates, Periods
from ..enums import LimitState
from ..TSDBReaderService import read_data_new
import statistics
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging

TS_API_HOST = config.get('api', 'api_host')
CSRF_TOKEN = config.get('api', 'csrf_token')

COOKIE = config.get('api', 'cookie')

# Initialize logger
logger = setup_logging()
# logging.basicConfig(level=logging.INFO)


# Function to fetch alerts by a list of IDs
def fetch_alerts_by_customer_id(customer_id: int):
    # Initialize the session
    db: Session = next(database.get_db())

    try:
        # Find the alerts by customer id
        alerts = db.query(Alerts).options(
            joinedload(Alerts.aggregate_enum),
            joinedload(Alerts.aggregate_period_enum),
            joinedload(Alerts.threshold_type_enum),
            joinedload(Alerts.comparison_enum)
        ).filter(models.Alerts.customer_id == customer_id, models.Alerts.deleted_at==None).all()

        return alerts

    except Exception as e:
        logger.exception(f"An error occurred: {e}")
        raise e

    finally:
        # Close the session
        db.close()


def fetch_alerts_by_ids(alert_ids: List[int]) -> list[Alerts]:
    # Initialize the session
    db: Session = next(database.get_db())

    try:
        # Find the alerts by IDs
        alerts = (db.query(models.Alerts).options(
            joinedload(Alerts.aggregate_enum),
            joinedload(Alerts.aggregate_period_enum),
            joinedload(Alerts.threshold_type_enum),
            joinedload(Alerts.comparison_enum)
        ).filter(models.Alerts.id.in_(alert_ids)).all())
        return alerts

    except Exception as e:
        logger.exception(f"An error occurred: {e}")
        raise e

    finally:
        # Close the session
        db.close()


# Function to fetch the state of an alert by ID
def fetch_alert_state(alert_id: int) -> LimitState:
    # Initialize the session
    db: Session = next(database.get_db())

    try:
        # Find the alert by ID
        alert = db.query(models.Alerts).filter(models.Alerts.id == alert_id).first()

        if alert:
            logger.debug(f"Alert ID = {alert.id}, State = {alert.state}")
            return LimitState.NORMAL if alert.state is None else LimitState[alert.state]
        else:
            raise Exception(f"Alert with ID {alert_id} not found.")

    except Exception as e:
        logger.exception(f"An error occurred: {e}")
        return None

    finally:
        # Close the session
        db.close()


# Function to update the state of an alert by ID
def update_alert_state(alert_id: int, new_state: str):
    # Initialize the session
    db: Session = next(database.get_db())

    try:
        # Find the alert by ID
        alert_to_update = db.query(models.Alerts).filter(models.Alerts.id == alert_id).first()

        if alert_to_update:
            # Update the state field
            alert_to_update.state = new_state

            # Commit the changes to the database
            db.commit()

            # Refresh the instance to reflect the changes in the session
            db.refresh(alert_to_update)

            logger.info(f"Alert with ID {alert_id} updated successfully.")
        else:
            logger.error(f"Alert with ID {alert_id} not found.")

    except Exception as e:
        # Rollback in case of error
        db.rollback()
        logger.exception(f"An error occurred: {e}")

    finally:
        # Close the session
        db.close()


# Function to fetch an event by ID
def fetch_event_by_id(event_id: int):
    # Initialize the session
    db: Session = next(database.get_db())

    try:
        # Find the event by ID
        event = db.query(models.Events).filter(models.Events.id == event_id).first()

        if event:
            logger.info(
                f"Event found: ID = {event.id}, Alert ID = {event.alert_id}, Timestamp = {event.timestamp}, State = {event.state}, Limit = {event.limit}, Comparator = {event.comparator}, Input Value = {event.input_value}")
            return event
        else:
            logger.error(f"Event with ID {event_id} not found.")
            return None

    except Exception as e:
        logger.exception(f"An error occurred: {e}")
        return None

    finally:
        # Close the session
        db.close()


def insert_event(alert_id: int, timestamp, state: str, limit: float, comparator: int, input_value: str,
                 deadband: float, aggregate:int, period:int, measurement_id:int, asset_id: int):
    db: Session = next(database.get_db())
    event_id = None
    try:
        # Convert the Unix timestamp to a UTC datetime object
        # timestamp = datetime.fromtimestamp(timestamp / 1000.0, tz=UTC)
        timestamp = datetime.fromtimestamp(timestamp / 1000.0)

        new_event = models.Events(
            alert_id=alert_id,
            timestamp=timestamp,
            state=state,
            limit=limit,
            comparator=comparator,
            input_value=input_value,
            deadband= deadband,
            aggregate = aggregate,
            period= period,
            measurement_id = measurement_id,
            asset_id = asset_id
        )
        db.add(new_event)
        db.commit()
        event_id = new_event.id
        logger.info(f"New event , ID {event_id} inserted for Alert ID {alert_id}")
    except Exception as e:
        logger.exception(f"An error occurred while inserting the event: {e}")
        db.rollback()
    finally:
        db.close()

    return event_id

import requests
def insert_state_execution(alert_id: int, end_time: datetime):
    db: Session = next(database.get_db())
    state_execution_id = None
    try:
        # Fetch the most recent EXCEEDED state timestamp from events table
        recent_event = db.query(models.Events).filter(
            models.Events.alert_id == alert_id,
            models.Events.state == 'EXCEEDED'
        ).order_by(models.Events.timestamp.desc()).first()

        if not recent_event:
            logger.info(f"No recent EXCEEDED state event found for Alert ID {alert_id}")
            return None

        # Fetch the alert details from the alerts table
        alert = db.query(models.Alerts).filter(models.Alerts.id == alert_id).first()
        if not alert:
            logger.info(f"Alert with ID {alert_id} not found.")
            return None
        
        # Convert start and end time to epoch milliseconds
        # Ensure timestamps are in UTC
        # start_time_epoch = int(recent_event.timestamp.astimezone(UTC).timestamp() * 1000)
        start_time_epoch = int(recent_event.timestamp.timestamp() * 1000)
        end_time_epoch = int(end_time)
        if alert.aggregate_enum.value and alert.aggregate_period_enum.value:
            logger.info(f"Period: {alert.aggregate_period_enum}, value: {alert.aggregate_period_enum.value} and label : {alert.aggregate_period_enum.label}")
            url = f"{TS_API_HOST}/timeseries/agg/{alert.customer_id}?meas_id={alert.measurement_id}&start={start_time_epoch}&end={end_time_epoch}&asset_tz=false&agg={alert.aggregate_enum.value}&agg_period={alert.aggregate_period_enum.label}"
        else:
            url = f"{TS_API_HOST}/timeseries/history/{alert.customer_id}?meas_id={alert.measurement_id}&start={start_time_epoch}&end={end_time_epoch}&asset_tz=false"

        headers = {
            "Be-Csrftoken": CSRF_TOKEN,
            "Cookie": COOKIE,
            "Connection": "keep-alive"
        }

        # Make synchronous GET request using requests
        try:
            logger.info(f"Sending GET request to {url} for excustion state")
            response = requests.get(url, headers=headers, timeout=30)

            logger.info(f"Received response with status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.debug(f"Response data: {data}")

                values = []
                for meas_obj in data:
                    ts_val = meas_obj.get('ts,val', [])
                    values.extend([pair[1] for pair in ts_val])

                if values:
                    # Calculate min, max, and average values
                    logger.info(f"Values fetched: {values}")
                    min_val = min(values)
                    max_val = max(values)
                    avg_val = statistics.mean(values)
                else:
                    min_val = max_val = avg_val = None

                logger.info(f"Processed data: min={min_val}, max={max_val}, avg={avg_val}")
            else:
                logger.error(f"Request failed with status {response.status_code}: {response.text}")
                raise Exception(f"Failed to fetch data: {response.text}")

        except requests.RequestException as e:
            logger.exception(f"HTTP request error: {e}")
            raise

        # Calculate the interval
        interval = end_time_epoch - start_time_epoch

        # Convert interval to human-readable format
        interval_seconds = interval / 1000
        interval_readable = str(timedelta(seconds=interval_seconds))

        # Create new ExcursionStates instance
        new_excursion_state = models.ExcursionStates(
            alert_id=alert_id,
            # start_time=datetime.fromtimestamp(start_time_epoch / 1000.0, tz=UTC),
            # end_time=datetime.fromtimestamp(end_time_epoch / 1000.0, tz=UTC),
            start_time=datetime.fromtimestamp(start_time_epoch / 1000.0) ,
            end_time=datetime.fromtimestamp(end_time_epoch / 1000.0),
            min_value=min_val,
            max_value=max_val,
            avg_value=avg_val,
            time_duration = interval_readable
        )

        # Add and commit the new instance to the database
        db.add(new_excursion_state)
        db.commit()
        state_execution_id = new_excursion_state.id
        logger.info(f"New state execution, ID {state_execution_id} inserted for Alert ID {alert_id}")
    except Exception as e:
        logger.exception(f"An error occurred while inserting the state execution: {e}")
        db.rollback()
    finally:
        db.close()

def get_alerts_by_measurement_id(measurement_id: int) -> list[Alerts]:
    """
    Get all alerts associated with a specific measurement_id.
    
    Args:
        measurement_id: The ID of the measurement to fetch alerts for
    
    Returns:
        list[Alerts]: List of Alert objects for the given measurement_id
    """
    db: Session = next(database.get_db())
    try:
        alerts = db.query(models.Alerts).options(
            joinedload(Alerts.aggregate_enum),
            joinedload(Alerts.aggregate_period_enum),
            joinedload(Alerts.threshold_type_enum),
            joinedload(Alerts.comparison_enum)
        ).filter(
            models.Alerts.measurement_id == measurement_id,
            # models.Alerts.enabled == True,
            models.Alerts.deleted_at == None
        ).all()
        
        return alerts
    except Exception as e:
        logger.exception(f"Error fetching alerts for measurement_id {measurement_id}: {e}")
        raise
    finally:
        db.close()

def disable_alerts(alert_ids: List[int]) -> None:
    """
    Set alerts as disabled (enabled=False) without marking them as deleted.
    
    Args:
        alert_ids: List of alert IDs to disable
    """
    db: Session = next(database.get_db())
    try:
        db.query(models.Alerts).filter(
            models.Alerts.id.in_(alert_ids)
        ).update({
            'enabled': False
        }, synchronize_session=False)
        
        db.commit()
    except Exception as e:
        db.rollback()
        logger.exception(f"Error disabling alerts: {e}")
        raise
    finally:
        db.close()

def mark_alerts_deleted(alert_ids: List[int], user_id: int) -> None:
    """
    Mark alerts as deleted by setting deleted_by and deleted_at fields.
    
    Args:
        alert_ids: List of alert IDs to mark as deleted
        user_id: ID of the user performing the deletion
    """
    db: Session = next(database.get_db())
    try:
        db.query(models.Alerts).filter(
            models.Alerts.id.in_(alert_ids)
        ).update({
            'deleted_by': user_id,
            'deleted_at': datetime.now(timezone.utc)
        }, synchronize_session=False)
        
        db.commit()
    except Exception as e:
        db.rollback()
        logger.exception(f"Error marking alerts as deleted: {e}")
        raise
    finally:
        db.close()

