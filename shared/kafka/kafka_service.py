import json
import logging

from confluent_kafka import Producer

from shared.alerts.result import AlertEvaluationResult
from shared.utils.config import config
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
logger= setup_logging()
# Kafka Configuration
KAFKA_BROKER = config.get('kafka', 'broker', fallback='localhost:9092')
KAFKA_TOPIC = config.get('kafka', 'alert_topic', fallback='alerts_topic')
# KAFKA_USERNAME = config.get('kafka', 'username', fallback='')
# KAFKA_PASSWORD = config.get('kafka', 'password', fallback='')

# KAFKA_BROKER = 'kafka-0.kafka.kafka.svc.cluster.local:9092'
# KAFKA_TOPIC = 'be-alert-dev-new'
# KAFKA_USERNAME = config.get('kafka', 'username', fallback='')
# KAFKA_PASSWORD = config.get('kafka', 'password', fallback='')

# logger = logging.getLogger(__name__)

# Global Kafka producer (reuse across sends)
kafka_producer = None

def setup_kafka_producer():
    global kafka_producer

    if kafka_producer is None:
        try:
            # Set up the Kafka producer configuration
            producer_config = {
                'bootstrap.servers': KAFKA_BROKER,
                'message.send.max.retries': 5,          # Number of retries on failure
                'retry.backoff.ms': 100,                # Time to wait before retrying (in milliseconds)
                'acks': 'all',                          # Ensure full acknowledgment for reliability
                'enable.idempotence': True              # Ensure message delivery exactly once
            }
            
            # # Set authentication if credentials are provided
            # if KAFKA_USERNAME and KAFKA_PASSWORD:
            #     producer_config.update({
            #         'security.protocol': 'SASL_SSL',
            #         'sasl.mechanisms': 'PLAIN',
            #         'sasl.username': KAFKA_USERNAME,
            #         'sasl.password': KAFKA_PASSWORD
            #     })

            # Initialize the Kafka producer
            producer = Producer(producer_config)
            logger.info(f"Kafka producer initialized,Connected to Kafka broker at {KAFKA_BROKER}")

        except Exception as e:
            logger.exception(f"Failed to create Kafka producer: {e}")
            raise
    return producer


def close_kafka_producer():
    global kafka_producer
    if kafka_producer is not None:
        try:
            kafka_producer.flush()  # Ensure all messages are sent before closing
            kafka_producer = None   # Set to None to allow for reinitialization if needed
            logger.info("Kafka producer closed")
        except Exception as e:
            logger.exception(f"Failed to close Kafka producer: {e}")


def delivery_report(err, msg):
    if err is not None:
        logger.error(f"Message delivery failed: {err}")
    else:
        logger.info(f"Message delivered to {msg.topic()} [{msg.partition()}] @ offset {msg.offset()}")

def send_kafka_notification(result: AlertEvaluationResult, event_id: int):
    # logger.info(f"Sending notification for event_id {event_id}")  # Log the event ID for tracking
    global kafka_producer
    kafka_producer = setup_kafka_producer()
    try:
        # Prepare the payload as JSON
        payload = json.dumps({
            "alert_id": result.alert_id,
            "state": result.state,
            "timestamp": result.timestamp,
            "measurement_id": result.measurement_id,
            "input_value": result.input_value,
            "limit": result.limit,
            "comparator": result.comparator,
            "deadband": result.deadband,
            "aggregate":result.aggregate,
            "period":result.period,
            "asset_id": result.asset_id,
            "event_id": event_id
        })
        
        # Publish the message
        kafka_producer.produce(KAFKA_TOPIC, key=str(event_id), value=payload, callback=delivery_report)
        kafka_producer.poll(1)  # Trigger delivery report callbacks
        logger.info(f"Notification sent to Kafka topic {KAFKA_TOPIC} with payload: {payload}")

        close_kafka_producer()
    except Exception as e:
        logger.exception(f"An error occurred while sending notification to Kafka: {e}")




