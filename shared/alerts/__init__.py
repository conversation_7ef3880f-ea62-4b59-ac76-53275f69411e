"""Alerts Package

This package provides shared alert models used by multiple services.
"""

# Import models
from shared.alerts.models import (
    AlertConfig,
    EvaluateAlertTask,
    CreateAlertTask,
    UpdateAlertTask,
    DeleteAlertTask
)

# Import result classes
from shared.alerts.result import AlertEvaluationResult

# Import utilities
from shared.alerts.utils.alert_utils import (
    group_alerts_by_measurement,
    group_alerts_by_aggregate_period,
    extract_alert_ids,
    format_alert_description,
    get_measurements_and_alerts_new
)

__all__ = [
    # Models
    'AlertConfig',
    'EvaluateAlertTask',
    'CreateAlertTask',
    'UpdateAlertTask',
    'DeleteAlertTask',

    # Result classes
    'AlertEvaluationResult',

    # Utilities
    'group_alerts_by_measurement',
    'group_alerts_by_aggregate_period',
    'extract_alert_ids',
    'format_alert_description',
    'get_measurements_and_alerts_new'
]
