#!/usr/bin/env python3
"""
Runtime Import Test

This script tests that imports can be resolved at runtime without Redis dependencies.
It focuses on testing the import structure without triggering external service connections.
"""

import sys
import os
import importlib.util

def test_basic_shared_imports():
    """Test basic shared imports that don't require external services."""
    print("🔍 Testing basic shared imports...")
    
    try:
        # Test context variables (should not require Redis)
        import contextvars
        customer_id_context = contextvars.ContextVar("customer_id", default="00")
        alert_id_context = contextvars.ContextVar("alert_id", default=None)
        print("  ✅ Context variables import OK")
    except Exception as e:
        print(f"  ❌ Context variables import error: {e}")
        return False
    
    try:
        # Test BromptonPythonUtilities
        from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
        print("  ✅ BromptonPythonUtilities import OK")
    except Exception as e:
        print(f"  ❌ BromptonPythonUtilities import error: {e}")
        return False
    
    return True

def test_module_structure():
    """Test that module structure is correct by checking file existence."""
    print("\n🔍 Testing module structure...")
    
    required_files = [
        "services/__init__.py",
        "services/scheduler-api/__init__.py", 
        "services/scheduler-api/app/__init__.py",
        "services/scheduler-api/app/main.py",
        "services/scheduler-api/app/celery_app.py",
        "services/scheduler-api/app/scheduler_service.py",
        "services/scheduler-api/app/middleware.py",
        "services/alert-engine-worker/__init__.py",
        "services/alert-engine-worker/app/__init__.py",
        "services/alert-engine-worker/app/celery_app.py",
        "services/alert-engine-worker/app/tasks/__init__.py",
        "services/alert-engine-worker/app/tasks/alert_tasks.py",
        "services/alert-engine-worker/app/services/__init__.py",
        "services/alert-engine-worker/app/services/evaluation_service.py",
        "services/alert-engine-worker/app/factory/__init__.py",
        "services/alert-engine-worker/app/factory/alert_factory.py",
        "services/celery-beat-engine/app/__init__.py",
        "services/celery-beat-engine/app/celery_app.py",
        "shared/__init__.py",
        "shared/alerts/__init__.py",
        "shared/alerts/models.py",
        "shared/db/__init__.py",
        "shared/db/db_service.py",
        "shared/utils/__init__.py",
        "shared/utils/config.py",
        "shared/utils/context.py",
        "shared/utils/enums.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"\n  ❌ Missing files:")
        for file_path in missing_files:
            print(f"    - {file_path}")
        return False
    
    return True

def test_import_resolution():
    """Test that Python can resolve the import paths correctly."""
    print("\n🔍 Testing import path resolution...")
    
    # Add current directory to Python path
    sys.path.insert(0, '.')
    
    # Test that we can find the modules without importing them
    test_modules = [
        "services.scheduler_api.app.main",
        "services.alert_engine_worker.app.celery_app", 
        "services.celery_beat_engine.app.celery_app",
        "shared.alerts.models",
        "shared.utils.context",
        "shared.utils.enums",
    ]
    
    # Note: We can't actually import these because they have Redis dependencies
    # But we can test if Python can find the module files
    
    for module_name in test_modules:
        try:
            # Convert module name to file path
            file_path = module_name.replace('.', '/') + '.py'
            
            # Handle special cases for directories with hyphens
            file_path = file_path.replace('scheduler_api', 'scheduler-api')
            file_path = file_path.replace('alert_engine_worker', 'alert-engine-worker')
            file_path = file_path.replace('celery_beat_engine', 'celery-beat-engine')
            
            if os.path.exists(file_path):
                print(f"  ✅ {module_name} -> {file_path}")
            else:
                print(f"  ❌ {module_name} -> {file_path} (not found)")
                return False
                
        except Exception as e:
            print(f"  ❌ {module_name}: {e}")
            return False
    
    return True

def test_relative_import_structure():
    """Test that relative imports are structured correctly."""
    print("\n🔍 Testing relative import structure...")
    
    # Test scheduler-api relative imports
    scheduler_files = {
        "services/scheduler-api/app/main.py": [
            "from .celery_app import app as celery_app",
            "from .scheduler_service import delete_task, create_task",
            "from .middleware import CustomMiddleware"
        ],
        "services/scheduler-api/app/scheduler_service.py": [
            "from .celery_app import app as celery_app"
        ]
    }
    
    # Test alert-engine-worker relative imports  
    alert_worker_files = {
        "services/alert-engine-worker/app/tasks/alert_tasks.py": [
            "from ..celery_app import app as celery_app",
            "from ..factory import AlertFactory",
            "from ..services import AlertEvaluationService"
        ]
    }
    
    all_files = {**scheduler_files, **alert_worker_files}
    
    for file_path, expected_imports in all_files.items():
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            missing_imports = []
            for expected_import in expected_imports:
                if expected_import not in content:
                    missing_imports.append(expected_import)
            
            if missing_imports:
                print(f"  ❌ {file_path}: Missing relative imports:")
                for imp in missing_imports:
                    print(f"    - {imp}")
                return False
            else:
                print(f"  ✅ {file_path}: All relative imports found")
                
        except FileNotFoundError:
            print(f"  ❌ {file_path}: File not found")
            return False
    
    return True

def main():
    """Run all runtime import tests."""
    print("🚀 Starting Runtime Import Tests\n")
    
    tests = [
        ("Basic Shared Imports", test_basic_shared_imports),
        ("Module Structure", test_module_structure),
        ("Import Path Resolution", test_import_resolution),
        ("Relative Import Structure", test_relative_import_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("RUNTIME IMPORT TEST SUMMARY")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All runtime import tests PASSED!")
        print("✅ Import structure is ready for deployment")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) FAILED")
        print("❌ Import structure needs attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
