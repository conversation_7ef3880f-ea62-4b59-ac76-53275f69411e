"""Celery Beat Engine Configuration

This module configures the Celery Beat application for scheduling alert evaluation tasks.
It uses shared Celery configuration utilities to ensure consistency.
"""

from celery import Celery
from kombu import Queue
from shared.utils.celery_config import create_celery_app, configure_task_routes
from shared.utils.config import config
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging

logger = setup_logging()

# Get broker URL from config
broker_url = f"redis://{config.get('celery', 'redis_host')}:{config.get('celery', 'redis_port')}/{config.get('celery', 'redis_db')}"

# class TraceLoggingFilter(logging.Filter):
#     def __init__(self):
#         super().__init__()
#         self.customer_id = None
#         self.alert_id = None
    
#     def set_context(self, customer_id=None, alert_id=None):
#         """Set the customer_id and alert_id for the logging context."""
#         self.customer_id = customer_id
#         self.alert_id = alert_id

#     def filter(self, record):
#         # Get the current span from OpenTelemetry
#         span = get_current_span()
#         if span and span.get_span_context():
#             record.trace_id = hex(span.get_span_context().trace_id)[2:].zfill(32)
#             record.span_id = hex(span.get_span_context().span_id)[2:].zfill(16)
#         else:
#             # Default values if no span context is available
#             record.trace_id = "00000000000000000000000000000000"
#             record.span_id = "0000000000000000"

#          # Add customer_id and alert_id
#         record.customer_id = self.customer_id if self.customer_id else "unknown"
#         record.alert_id = self.alert_id if self.alert_id else "unknown"
#         return True

# logging_filter = TraceLoggingFilter()
# @after_setup_logger.connect
# def setup_celery_logger(logger, *args, **kwargs):
#     """Configure the Celery root logger with JSON formatting and tracing."""
#     handler = logging.StreamHandler()
#     json_formatter = jsonlogger.JsonFormatter(
#         fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
#     )
#     handler.setFormatter(json_formatter)
#     handler.addFilter(logging_filter)  # Retain tracing filter

#     logger.handlers.clear()
#     logger.addHandler(handler)


# @after_setup_task_logger.connect
# def setup_celery_task_logger(logger, *args, **kwargs):
#     """Configure the Celery task logger with JSON formatting and tracing."""
#     handler = logging.StreamHandler()
#     json_formatter = jsonlogger.JsonFormatter(
#         fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
#     )
#     handler.setFormatter(json_formatter)
#     handler.addFilter(logging_filter)  # Retain tracing filter

#     logger.handlers.clear()
#     logger.addHandler(handler)


# Example usage
# logger = logging.getLogger(__name__)

app = Celery(
    'app',
    broker=broker_url,
    backend=broker_url,
    broker_connection_retry_on_startup=True
)

app.conf.update(
    result_expires=3600,
    timezone='UTC',
    enable_utc=True,
    beat_scheduler='redbeat.RedBeatScheduler',  # Use RedBeatScheduler
    redbeat_redis_url=broker_url,  # Redis URL for redbeat
    redbeat_lock_key='redbeat::lock',  # Lock key for redbeat
    beat_max_loop_interval=5,  # Shorten the interval for checking the schedule
    sync_every_tasks=True,
    worker_pool='threads',
    worker_hijack_root_logger=False,  # Prevent Celery from overriding the root logger
    
    # Task execution settings
    task_acks_late=True,  # Only acknowledge after task completion
    task_time_limit=600,  # Hard time limit of 10 minutes
    task_soft_time_limit=300,  # Soft time limit of 5 minutes
    worker_prefetch_multiplier=1,  # Prevent worker from prefetching too many tasks
    worker_concurrency=4,  # Number of worker processes
    
    # Task retry settings
    task_default_retry_delay=60,  # Default retry delay in seconds
    task_max_retries=1,  # Maximum number of retries
    task_default_expires =1800,  # Default expiration time for tasks
)

app.conf.task_queues = (
    Queue('evaluate_alert_task', routing_key='task.evaluate_alert.#'),
)

app.conf.task_routes = {
    'tasks.evaluate_alert_task': {'queue': 'evaluate_alert_task'},
}

# Define the Celery Beat schedule
# app.conf.beat_schedule = {
#     'run-every-20-seconds': {
#         'task': 'app.tasks.evaluate_alert_task',
#         'schedule': schedule(run_every=1),  # Run the task every 20 seconds
#         'args': ['8_twa_15min', '{"customer_id":8,"aggregate":"twa","aggregate_period":"15min","alert_ids":[368]}'],  # Arguments to pass to the task
#     },
# }


# Ensure tasks are registered
app.autodiscover_tasks(['app'])
celery_app = app