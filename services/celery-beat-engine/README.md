# Celery Beat Engine Service

This service is responsible for scheduling alert evaluation tasks using Celery Beat with <PERSON>is as the scheduler backend.

## Overview

The Celery Beat Engine is a microservice that:
- Schedules periodic alert evaluation tasks
- Uses RedBeat for Redis-based scheduling
- Manages task scheduling and lifecycle
- Provides monitoring and metrics

## Directory Structure

```
services/celery-beat-engine/
├── app/
│   ├── __init__.py                  # Service initialization
│   ├── celery_app.py                # Celery Beat configuration
│   ├── config.py                    # Service-specific configuration
│   ├── prometheus_metrices.py       # Metrics collection
│   └── redis.py                     # Redis utilities
├── venv/                            # Virtual environment
├── requirements.txt                 # Python dependencies
├── setup_venv.sh                    # Virtual environment setup script
├── Dockerfile                       # Container configuration
├── .gitignore                       # Git ignore rules
└── README.md                        # This file
```

## Setup

### Prerequisites

- Python 3.10+
- Redis server
- Access to shared utilities and BromptonPythonUtilities

### Installation

1. **Set up virtual environment:**
   ```bash
   cd services/celery-beat-engine
   ./setup_venv.sh
   ```

2. **Activate virtual environment:**
   ```bash
   source venv/bin/activate
   ```

3. **Verify installation:**
   ```bash
   python -c "import celery; print('Celery version:', celery.__version__)"
   python -c "import redbeat; print('RedBeat imported successfully')"
   ```

## Configuration

The service uses configuration from the root `config.ini` file. Key configuration sections:

- `[celery]`: Redis connection settings
- `[jwt]`: Authentication settings

## Running the Service

### Development

```bash
# From the repository root directory
cd /path/to/alert-service
source services/celery-beat-engine/venv/bin/activate
celery -A services.celery_beat_engine.app.celery_app beat --loglevel=info
```

### Production

The service is designed to run in Kubernetes. See the deployment manifests in the `kubernetes/` directory.

## Dependencies

### Core Dependencies
- `celery[redis]==5.3.4` - Task queue framework
- `redis>=4.5.2,<5.0.0` - Redis client
- `celery-redbeat==2.2.0` - Redis-based beat scheduler
- `pydantic==2.5.0` - Data validation
- `prometheus-client==0.19.0` - Metrics collection

### Shared Dependencies
- `shared/` - Shared utilities and models
- `BromptonPythonUtilities/` - Common utilities

## Monitoring

The service exposes Prometheus metrics for monitoring:
- Task scheduling metrics
- Redis connection status
- Beat scheduler health

## Architecture

The service integrates with:
- **Redis**: For task scheduling and state management
- **Alert Engine Worker**: Receives scheduled tasks
- **Shared utilities**: For configuration and common functionality

## Development

### Adding New Scheduled Tasks

1. Define the task in the alert-engine-worker service
2. Configure the schedule in the beat configuration
3. Test the scheduling and execution

### Testing

Run tests from the repository root:
```bash
# Unit tests
python -m pytest services/celery-beat-engine/tests/

# Integration tests with Redis
docker-compose up -d redis
python -m pytest services/celery-beat-engine/tests/ --integration
```

## Troubleshooting

### Common Issues

1. **Redis connection errors**: Verify Redis is running and accessible
2. **Import errors**: Ensure you're running from the repository root
3. **Configuration errors**: Check `config.ini` file exists and is readable

### Logs

The service uses structured JSON logging. Check logs for:
- Beat scheduler status
- Task scheduling events
- Redis connection status
- Error messages

## Contributing

When making changes:
1. Update dependencies in `requirements.txt`
2. Run tests to ensure compatibility
3. Update documentation as needed
4. Follow the established code structure
