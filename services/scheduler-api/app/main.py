from typing import Tu<PERSON>, List, Dict
from shared.utils.redis import master
import time
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from redbeat import RedBeatSchedulerEntry
# Import celery app from local module instead of from another service
from .celery_app import app as celery_app

from shared.db.db_service import fetch_alerts_by_customer_id
from shared.utils.enums import Aggregate, AggregatePeriod
# Import models from shared location
from shared.alerts.models import EvaluateAlertTask, CreateAlertTask, UpdateAlertTask, DeleteAlertTask, AlertConfig
from .scheduler_service import delete_task, create_task
# Import utility functions from shared location
from shared.alerts.utils.alert_utils import get_measurements_and_alerts_new
import asyncio
import platform
from prometheus_fastapi_instrumentator import Instrumentator
from shared.db.db_service import get_alerts_by_measurement_id, mark_alerts_deleted, disable_alerts
from shared.utils.prometheus_metrices import  TASKS_CREATED, TASKS_DELETED, ACTIVE_ALERTS, REDIS_CONNECTION_STATUS, API_LATENCY, TASK_FAILURES
from prometheus_client.core import CollectorRegistry
from .middleware import CustomMiddleware
from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from shared.utils.auth import (
    hasCustomerScope, hasCustomerScopeWithRole,
    JWTAuth, UnicornException, decode_jwt
)

logger= setup_logging()

# Conditionally set uvloop for non-Windows platforms
if platform.system() != "Windows":
    try:
        import uvloop
        asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        print("Using uvloop for improved performance on supported OS.")
    except ImportError:
        print("uvloop is not installed; using the default asyncio event loop.")
else:
    print("uvloop is not supported on Windows; using default event loop.")



# Apply global logging configuration
# setup_logging()

# # Example usage
# logger = logging.getLogger(__name__)

app = FastAPI(dependencies=[Depends(JWTAuth())])
app.add_middleware(CustomMiddleware)
# Reset the default registry
REGISTRY = CollectorRegistry()
# Initialize Prometheus Instrumentator
Instrumentator().instrument(app).expose(app)
@app.exception_handler(UnicornException)
async def unicorn_exception_handler(request: Request, exc: UnicornException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message},
    )

class TaskSchedule(BaseModel):
    task_name: str
    args: AlertConfig
    interval_seconds: int


@hasCustomerScopeWithRole(roles=["USER"])
@app.get("/read-data/{customer_id}/{measurement_id}")
async def read_data(customer_id: int, measurement_id: int,
                    aggregate: Aggregate, period: AggregatePeriod):
    """
    Endpoint to read data from the TSDB.
    """
    start_time = time.time()
    # Example measurement dictionary for demonstration purposes
    measurements: Dict[Tuple[str, int], List[Tuple[str, int]]] = {
        ("AssetPath", customer_id): [("MeasurementTag", measurement_id)]
    }

    try:
        data = await read_data(
            customer=customer_id,
            measurements=measurements,
            aggregate=aggregate,
            period=period,
            timeout=30
        )
        if data:
            API_LATENCY.labels(endpoint="/read-data", method="GET").observe(time.time() - start_time)
            return data
        else:
            API_LATENCY.labels(endpoint="/read-data", method="GET").observe(time.time() - start_time)
            raise HTTPException(status_code=404, detail="Measurement data not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@hasCustomerScopeWithRole(roles=["USER"])
@app.post("/schedule_task_new/")
async def schedule_task(task: EvaluateAlertTask, interval_seconds: int = 60):
    task_name = f"{task.customer_id}_{task.aggregate.name}_{task.aggregate_period.name}"
    try:
        await create_task(task_name, task, interval_seconds)
        TASKS_CREATED.labels(task_name=task_name).inc()
        return {"message": f"Task {task_name} scheduled successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@hasCustomerScopeWithRole(roles=["USER"])
@app.post("/alert")
async def create_alert(task: CreateAlertTask):
    logger.info(f"create req: {task}")
    customer_id = task.customer_id
    alert_configs = fetch_alerts_by_customer_id(customer_id)
    agg_group_to_meas_alert_config = get_measurements_and_alerts_new(alert_configs)

    for agg_group, meas_alert_config in agg_group_to_meas_alert_config.items():
        agg = agg_group[0]
        period = agg_group[1]
        alert_ids = [alert_config.id for alert_config in meas_alert_config.values() if alert_config.enabled is True]

        task_name = f"{customer_id}_{agg}_{period}"
        await delete_task(task_name)
        if alert_ids:
            await create_task(
                task_name=task_name,
                task=EvaluateAlertTask(
                    customer_id=customer_id,
                    aggregate=agg,
                    aggregate_period=period,
                    alert_ids=alert_ids
                ),
                interval_seconds=20
            )
            ACTIVE_ALERTS.labels(customer_id=customer_id, aggregate=agg, period=period).set(len(alert_ids))

@hasCustomerScopeWithRole(roles=["USER"])
@app.put("/alert")
async def update_alert(task: UpdateAlertTask):
    logger.info(f"update req: {task}")
    customer_id = task.customer_id
    alert_configs = fetch_alerts_by_customer_id(customer_id)
    agg_group_to_meas_alert_config = get_measurements_and_alerts_new(alert_configs)

    await delete_task(f"{customer_id}_{task.previous_aggregate.value}_{task.previous_aggregate_period.value}")
    for agg_group, meas_alert_config in agg_group_to_meas_alert_config.items():
        agg = agg_group[0]
        period = agg_group[1]
        alert_ids = [alert_config.id for alert_config in meas_alert_config.values() if alert_config.enabled is True]

        task_name = f"{customer_id}_{agg}_{period}"
        await delete_task(task_name)
        if alert_ids:
            await create_task(
                task_name=task_name,
                task=EvaluateAlertTask(
                    customer_id=customer_id,
                    aggregate=agg,
                    aggregate_period=period,
                    alert_ids=alert_ids
                ),
                interval_seconds=20
            )

@hasCustomerScopeWithRole(roles=["USER"])
@app.delete("/alert")
async def delete_alert(task: DeleteAlertTask):
    logger.info(f"delete req: {task}")
    customer_id = task.customer_id
    alert_configs = fetch_alerts_by_customer_id(customer_id)
    agg_group_to_meas_alert_config = get_measurements_and_alerts_new(alert_configs)

    for agg_group, meas_alert_config in agg_group_to_meas_alert_config.items():
        agg = agg_group[0]
        period = agg_group[1]

        if agg == task.aggregate.value and period == task.aggregate_period.value:
            alert_ids = [alert_config.id for alert_config in meas_alert_config.values() if alert_config.enabled is True]
            task_name = f"{customer_id}_{agg}_{period}"
            await delete_task(task_name)
            if alert_ids:
                await create_task(
                    task_name=task_name,
                    task=EvaluateAlertTask(
                        customer_id=customer_id,
                        aggregate=agg,
                        aggregate_period=period,
                        alert_ids=alert_ids
                    ),
                    interval_seconds=20
                )

@hasCustomerScopeWithRole(roles=["USER"])
@app.post("/schedule_task/")
async def schedule_task(schedule: TaskSchedule):
    try:
        task_entry = RedBeatSchedulerEntry(
            name=schedule.task_name,
            task='app.tasks.evaluate_alert',
            schedule=schedule.interval_seconds,
            args=[schedule.args.json()],  # Serialize the AlertConfig object to JSON string and wrap it in a list
            app=celery_app,
            enabled=True,
        )
        task_entry.save()
        TASKS_CREATED.labels(task_name=schedule.task_name).inc()
        return {"message": f"Task {schedule.task_name} scheduled successfully"}
    except Exception as e:
        TASK_FAILURES.labels(task_name=schedule.task_name).inc()
        raise HTTPException(status_code=400, detail=str(e))

@hasCustomerScopeWithRole(roles=["USER"])
@app.delete("/delete_task/{task_name}")
def delete_sch_task(task_name: str):
    try:
        # Check if the task exists
        entry = RedBeatSchedulerEntry.from_key('redbeat:' + task_name, app=celery_app)
        if entry:
            # Remove the entry from Redis
            master.delete('redbeat:' + task_name)
            TASKS_DELETED.labels(task_name=task_name).inc()
            return {"message": f"Task {task_name} deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Task not found")
    except Exception as e:
        print(e)
        raise HTTPException(status_code=400, detail=str(e))


async def delete_alert_for_measurement(task: DeleteAlertTask):
    logger.info(f"delete req: {task}")
    customer_id = task.customer_id
    alert_configs = fetch_alerts_by_customer_id(customer_id)
    agg_group_to_meas_alert_config = get_measurements_and_alerts_new(alert_configs)

    for agg_group, meas_alert_config in agg_group_to_meas_alert_config.items():
        agg = agg_group[0]
        period = agg_group[1]

        if agg == task.aggregate.value and period == task.aggregate_period.value:
            alert_ids = [alert_config.id for alert_config in meas_alert_config.values() if alert_config.enabled is True]
            task_name = f"{customer_id}_{agg}_{period}"
            await delete_task(task_name)
            if alert_ids:
                await create_task(
                    task_name=task_name,
                    task=EvaluateAlertTask(
                        customer_id=customer_id,
                        aggregate=agg,
                        aggregate_period=period,
                        alert_ids=alert_ids
                    ),
                    interval_seconds=20
                )

@hasCustomerScopeWithRole(roles=["USER"])
@app.delete("/alerts/measurement/{measurement_id}")
async def delete_measurement_alerts(
    measurement_id: int,
    request: Request
):
    try:
        # Get user info from JWT token
        access_token = request.cookies.get("BE-AccessToken")
        if not access_token:
            raise HTTPException(status_code=401, detail="Missing access token")
        user_data = decode_jwt(access_token)
        # logger.info(f"JWT token data: {user_data}")  # Log token contents
        user_id = user_data.get('sub')
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        # Get all alerts for this measurement_id
        alerts = get_alerts_by_measurement_id(measurement_id)

        if not alerts:
            return {
                "status": "success",
                "alerts_deleted": 0,
                "tasks_deleted": 0,
                "message": f"No alerts found for measurement_id {measurement_id}"
            }

        # First disable all alerts
        alert_ids = [alert.id for alert in alerts]
        if alert_ids:
            disable_alerts(alert_ids)

        deleted_alerts = 0
        deleted_tasks = 0
        successful_alert_ids = []

        # Then delete tasks and mark alerts as deleted
        for alert in alerts:
            try:
                # Check if the alert is already deleted
                # Create DeleteAlertTask with alert's information
                delete_task_obj = DeleteAlertTask(
                    customer_id=alert.customer_id,
                    aggregate=Aggregate[alert.aggregate_enum.label],
                    aggregate_period=AggregatePeriod[alert.aggregate_period_enum.value],
                    alert_id=alert.id
                )

                # Call existing delete_alert endpoint logic and check response
                await delete_alert_for_measurement(delete_task_obj)

                deleted_alerts += 1
                deleted_tasks += 1
                successful_alert_ids.append(alert.id)

            except Exception as e:
                logger.error(f"Error deleting alert {alert.id}: {e}")
                continue

        # Mark only successfully deleted alerts
        if successful_alert_ids:
            mark_alerts_deleted(successful_alert_ids, user_id)

        return {
            "status": "success",
            "alerts_deleted": deleted_alerts,
            "tasks_deleted": deleted_tasks,
            "message": f"Successfully deleted {deleted_alerts} alerts and their associated tasks"
        }

    except Exception as e:
        logger.exception(f"Error in delete_measurement_alerts: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete alerts for measurement {measurement_id}: {str(e)}"
        )

@app.get('/health_check')
async def health_check():
    try:
        # Test Redis connection by pinging
        master.ping()
        REDIS_CONNECTION_STATUS.set(1)  # Healthy
        return {"status": "Healthy", "details": "Redis is reachable"}
    except ConnectionError:
        REDIS_CONNECTION_STATUS.set(0)  # Unhealthy
        raise HTTPException(status_code=503, detail="Redis is not reachable")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000)
