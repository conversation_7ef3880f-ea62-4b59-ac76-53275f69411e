#!/bin/bash

# Scheduler API Virtual Environment Setup Script
# This script sets up a virtual environment for the scheduler-api service

set -e  # Exit on any error

echo "Setting up virtual environment for scheduler-api service..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_DIR="$SCRIPT_DIR"

# Navigate to service directory
cd "$SERVICE_DIR"

# Remove existing venv if it exists
if [ -d "venv" ]; then
    echo "Removing existing virtual environment..."
    rm -rf venv
fi

# Create new virtual environment
echo "Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip setuptools wheel

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Verify installation
echo "Verifying installation..."
python -c "import fastapi; print(f'FastAPI version: {fastapi.__version__}')"
python -c "import celery; print(f'Celery version: {celery.__version__}')"
python -c "import redis; print(f'Redis version: {redis.__version__}')"
python -c "import redbeat; print('RedBeat imported successfully')"
python -c "import uvicorn; print(f'Uvicorn imported successfully')"

echo "Virtual environment setup complete!"
echo ""
echo "To activate the virtual environment, run:"
echo "  source venv/bin/activate"
echo ""
echo "To deactivate, run:"
echo "  deactivate"
