# Scheduler API Service

This service provides a REST API for creating, updating, and managing alert schedules using FastAPI and Celery Beat.

## Overview

The Scheduler API is a microservice that:
- Provides REST endpoints for alert schedule management
- Integrates with Celery Beat for task scheduling
- Handles authentication and authorization
- Provides monitoring and metrics via Prometheus

## Directory Structure

```
services/scheduler-api/
├── app/
│   ├── __init__.py                  # Service initialization
│   ├── main.py                      # FastAPI application
│   ├── celery_app.py                # Celery configuration
│   ├── scheduler_service.py         # Scheduling business logic
│   └── middleware.py                # Custom middleware
├── venv/                            # Virtual environment
├── requirements.txt                 # Python dependencies
├── setup_venv.sh                    # Virtual environment setup script
├── Dockerfile                       # Container configuration
├── .gitignore                       # Git ignore rules
└── README.md                        # This file
```

## Setup

### Prerequisites

- Python 3.10+
- Redis server
- Access to shared utilities and BromptonPythonUtilities

### Installation

1. **Set up virtual environment:**
   ```bash
   cd services/scheduler-api
   ./setup_venv.sh
   ```

2. **Activate virtual environment:**
   ```bash
   source venv/bin/activate
   ```

3. **Verify installation:**
   ```bash
   python -c "import fastapi; print('FastAPI version:', fastapi.__version__)"
   python -c "import celery; print('Celery version:', celery.__version__)"
   python -c "import uvicorn; print('Uvicorn imported successfully')"
   ```

## Configuration

The service uses configuration from the root `config.ini` file. Key configuration sections:

- `[celery]`: Redis connection settings
- `[jwt]`: Authentication settings

## Running the Service

### Development

```bash
# From the repository root directory
cd /path/to/alert-service
source services/scheduler-api/venv/bin/activate
uvicorn services.scheduler_api.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Production

The service is designed to run in Kubernetes. See the deployment manifests in the `kubernetes/` directory.

## API Endpoints

### Authentication

All endpoints require JWT authentication with appropriate customer scope.

### Schedule Management

- `POST /schedule_task_new/` - Create a new alert schedule
- `DELETE /delete_task_new/` - Delete an alert schedule
- `GET /get_tasks/` - List all scheduled tasks
- `GET /get_task/{task_name}` - Get specific task details

### Health and Monitoring

- `GET /health` - Health check endpoint
- `GET /metrics` - Prometheus metrics

## Dependencies

### Core Dependencies
- `fastapi==0.112.0` - Web framework
- `uvicorn[standard]==0.30.1` - ASGI server
- `celery[redis]==5.3.4` - Task queue framework
- `redis>=4.5.2,<5.0.0` - Redis client
- `celery-redbeat==2.2.0` - Redis-based beat scheduler
- `pydantic==2.5.0` - Data validation
- `PyJWT==2.9.0` - JWT authentication
- `prometheus-client==0.19.0` - Metrics collection
- `prometheus-fastapi-instrumentator==7.0.0` - FastAPI metrics

### Shared Dependencies
- `shared/` - Shared utilities and models
- `BromptonPythonUtilities/` - Common utilities

## Authentication

The API uses JWT-based authentication with customer scoping:

```python
from shared.utils.auth import hasCustomerScopeWithRole

@hasCustomerScopeWithRole(roles=["USER"])
@app.post("/schedule_task_new/")
async def schedule_task(task: EvaluateAlertTask):
    # Implementation
```

## Monitoring

The service exposes Prometheus metrics:
- API request metrics
- Task creation/deletion counters
- Active alert counts
- Redis connection status
- API latency histograms

## Architecture

The service integrates with:
- **Redis**: For task scheduling and state management
- **Celery Beat Engine**: For task scheduling
- **Alert Engine Worker**: Target for scheduled tasks
- **Database**: For alert configuration retrieval
- **Shared utilities**: For configuration and common functionality

## Development

### Adding New Endpoints

1. Define the endpoint in `main.py`
2. Add business logic to `scheduler_service.py`
3. Update authentication decorators as needed
4. Add appropriate error handling and logging

### Testing

Run tests from the repository root:
```bash
# Unit tests
python -m pytest services/scheduler-api/tests/

# Integration tests
docker-compose up -d redis
python -m pytest services/scheduler-api/tests/ --integration
```

## API Usage Examples

### Create Alert Schedule

```bash
curl -X POST "http://localhost:8000/schedule_task_new/" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "aggregate": "avg",
    "aggregate_period": "5min",
    "alert_ids": [123, 456]
  }'
```

### Delete Alert Schedule

```bash
curl -X DELETE "http://localhost:8000/delete_task_new/" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "aggregate": "avg",
    "aggregate_period": "5min"
  }'
```

## Troubleshooting

### Common Issues

1. **Authentication errors**: Verify JWT token and customer scope
2. **Redis connection errors**: Verify Redis is running and accessible
3. **Import errors**: Ensure you're running from the repository root
4. **Configuration errors**: Check `config.ini` file exists and is readable

### Logs

The service uses structured JSON logging. Check logs for:
- API request/response details
- Task scheduling events
- Authentication failures
- Redis connection status
- Error messages

## Contributing

When making changes:
1. Update dependencies in `requirements.txt`
2. Run tests to ensure compatibility
3. Update API documentation
4. Follow the established code structure
5. Ensure proper authentication is maintained
