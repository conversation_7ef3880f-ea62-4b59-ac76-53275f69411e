[flake8]
# Configuration for flake8 linting

# Maximum line length
max-line-length = 120

# Ignore specific error codes
extend-ignore = 
    E203,  # whitespace before ':'
    W503,  # line break before binary operator
    E501,  # line too long (handled by black)

# Exclude directories
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .pytest_cache,
    build,
    dist,
    *.egg-info

# Maximum complexity
max-complexity = 10

# Import order style
import-order-style = google

# Show source code for errors
show-source = True

# Show pep8 violation statistics
statistics = True

# Count errors and warnings
count = True
