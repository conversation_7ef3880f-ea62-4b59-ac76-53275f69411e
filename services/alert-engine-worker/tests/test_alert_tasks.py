"""Tests for alert tasks

This module contains tests for the alert evaluation tasks.
"""

import json
import pytest
from unittest.mock import patch, MagicMock


class TestAlertTasks:
    """Test cases for alert tasks."""

    @pytest.mark.unit
    @patch('app.tasks.alert_tasks.async_to_sync')
    def test_evaluate_alert_task(self, mock_async_to_sync):
        """Test the evaluate_alert_task function."""
        from app.tasks.alert_tasks import evaluate_alert_task
        from shared.utils.enums import Aggregate, AggregatePeriod

        # Mock the async_to_sync function
        mock_async_to_sync.return_value = MagicMock(return_value=True)

        # Create test data
        task_name = "test_task"
        task_data = {
            "customer_id": 1,
            "alert_ids": [1, 2, 3],
            "aggregate": Aggregate.TWA,
            "aggregate_period": AggregatePeriod._15M
        }
        task_json = json.dumps(task_data)

        # Call the function
        result = evaluate_alert_task(task_name, task_json)

        # Assert the result
        assert result is True

        # Assert that async_to_sync was called with the correct arguments
        mock_async_to_sync.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.tasks.alert_tasks.fetch_alerts_by_ids')
    @patch('app.tasks.alert_tasks.AlertEvaluationService.evaluate_alerts')
    async def test_evaluate_alert_task_async(self, mock_evaluate_alerts, mock_fetch_alerts):
        """Test the evaluate_alert_task_async function."""
        from app.tasks.alert_tasks import evaluate_alert_task_async
        from shared.alerts.models import EvaluateAlertTask
        from shared.utils.enums import Aggregate, AggregatePeriod

        # Mock the fetch_alerts_by_ids function
        mock_fetch_alerts.return_value = [{"id": 1}, {"id": 2}, {"id": 3}]

        # Mock the evaluate_alerts function
        mock_evaluate_alerts.return_value = True

        # Create test data
        task = EvaluateAlertTask(
            customer_id=1,
            alert_ids=[1, 2, 3],
            aggregate=Aggregate.TWA,
            aggregate_period=AggregatePeriod._15M
        )

        # Call the function
        result = await evaluate_alert_task_async(task)

        # Assert the result
        assert result is True

        # Assert that fetch_alerts_by_ids was called with the correct arguments
        mock_fetch_alerts.assert_called_once_with([1, 2, 3])

        # Assert that evaluate_alerts was called with the correct arguments
        mock_evaluate_alerts.assert_called_once_with(
            customer_id=1,
            alert_configs=[{"id": 1}, {"id": 2}, {"id": 3}],
            aggregate=Aggregate.TWA,
            period=AggregatePeriod._15M
        )
