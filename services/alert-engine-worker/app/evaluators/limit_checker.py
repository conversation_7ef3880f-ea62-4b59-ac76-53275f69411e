from datetime import datetime
from typing import Optional, Union

from shared.utils.enums import CompareOperation, LimitState

from ..base import AlertEvaluationResult


def check_limit(
    alert_id: int,
    measurement_id: int,
    limit: Union[float, int, bool],
    deadband: Union[float, int],
    comparator: CompareOperation,
    timestamp: datetime,
    input_value: Union[float, int, bool],
    aggregate: str,
    period: str,
    asset_id: int,
) -> Optional[AlertEvaluationResult]:
    """
    Checks if a measurement value exceeds a limit based on the provided comparator.
    Returns an AlertEvaluationResult with the appropriate state.

    Args:
        alert_id: The ID of the alert
        measurement_id: The ID of the measurement
        limit: The threshold value
        deadband: The deadband value
        comparator: The comparison operator
        timestamp: The timestamp of the evaluation
        input_value: The current value of the measurement
        aggregate: The aggregation type
        period: The aggregation period
        asset_id: The ID of the asset

    Returns:
        AlertEvaluationResult with the appropriate state, or None if no state change
    """
    # Convert timestamp to datetime if it's a Unix timestamp
    if isinstance(timestamp, (int, float)):
        timestamp = datetime.fromtimestamp(timestamp / 1000.0)

    # Determine the current state based on the comparison
    if comparator == CompareOperation.EQ:
        exceeded = input_value == limit
    elif comparator == CompareOperation.LT:
        exceeded = input_value < limit
    elif comparator == CompareOperation.LE:
        exceeded = input_value <= limit
    elif comparator == CompareOperation.GT:
        exceeded = input_value > limit
    elif comparator == CompareOperation.GE:
        exceeded = input_value >= limit
    else:
        raise ValueError(f"Unsupported comparator: {comparator}")

    # Determine the new state
    new_state = LimitState.EXCEEDED if exceeded else LimitState.NORMAL

    # Return the result
    return AlertEvaluationResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=timestamp,
        state=new_state.name,
        limit=limit,
        comparator=str(comparator.value),
        input_value=input_value,
        deadband=deadband,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id,
    )
