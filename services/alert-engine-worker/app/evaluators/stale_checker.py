from datetime import datetime
from typing import Optional, Union

from shared.utils.enums import LimitState
from shared.utils.redis import master

from ..base import AlertEvaluationResult


def get_last_value(measurement_id: int) -> Optional[float]:
    """
    Get last stored value from Redis.

    Args:
        measurement_id: The ID of the measurement

    Returns:
        The last stored value, or None if not found
    """
    key = f"measurement:{measurement_id}:last_value"
    value = master.get(key)
    return float(value) if value is not None else None


def store_value(measurement_id: int, value: float) -> None:
    """
    Store current value in Redis.

    Args:
        measurement_id: The ID of the measurement
        value: The value to store
    """
    key = f"measurement:{measurement_id}:last_value"
    master.set(key, str(value), ex=86400)  # Expire after 24 hours


def check_stale_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_changed: Optional[datetime],
    stale_duration_seconds: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    current_value: Optional[Union[float, int, bool]] = None,
) -> Optional[AlertEvaluationResult]:
    """
    Determines if a measurement should be marked STALE based on lack of change.
    Returns an AlertEvaluationResult with the appropriate state.

    Args:
        alert_id: The ID of the alert
        measurement_id: The ID of the measurement
        now: The current timestamp
        last_changed: The timestamp when the measurement last changed
        stale_duration_seconds: The duration in seconds after which a measurement is considered stale
        aggregate: The aggregation type
        period: The aggregation period
        asset_id: The ID of the asset
        comparator_id: The ID of the comparator
        current_value: The current value of the measurement

    Returns:
        AlertEvaluationResult with the appropriate state, or None if no state change
    """
    # Get the last value from Redis
    last_value = get_last_value(measurement_id)

    # If we have a current value, store it
    if current_value is not None:
        store_value(measurement_id, float(current_value))

    # If we don't have a last value or current value, we can't determine staleness
    if last_value is None or current_value is None:
        return None

    # If the value has changed, update the last_changed timestamp
    if abs(float(current_value) - last_value) > 0.0001:
        # Value has changed, reset the stale timer
        last_changed = now

    # Determine if the measurement is stale
    if not last_changed or (now - last_changed).total_seconds() > stale_duration_seconds:
        new_state = LimitState.STALE
    else:
        new_state = LimitState.NORMAL

    return AlertEvaluationResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=now,
        state=new_state.name,
        limit=stale_duration_seconds,
        comparator=str(comparator_id),
        input_value=current_value,
        deadband=0,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id,
    )
