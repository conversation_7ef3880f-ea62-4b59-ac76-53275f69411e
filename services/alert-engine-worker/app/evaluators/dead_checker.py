from datetime import datetime
from typing import Optional, Union

from shared.utils.enums import LimitState

from ..base import AlertEvaluationResult


def check_dead_measurement(
    alert_id: int,
    measurement_id: int,
    now: datetime,
    last_seen: Optional[datetime],
    dead_duration_seconds: int,
    aggregate: str,
    period: str,
    asset_id: int,
    comparator_id: int,
    current_value: Optional[Union[str, int, float]],
) -> Optional[AlertEvaluationResult]:
    """
    Determines if a measurement should be marked DEAD based on absence of data.
    Returns an AlertEvaluationResult with the appropriate state.

    Args:
        alert_id: The ID of the alert
        measurement_id: The ID of the measurement
        now: The current timestamp
        last_seen: The timestamp when the measurement was last seen
        dead_duration_seconds: The duration in seconds after which a measurement is considered dead
        aggregate: The aggregation type
        period: The aggregation period
        asset_id: The ID of the asset
        comparator_id: The ID of the comparator
        current_value: The current value of the measurement

    Returns:
        AlertEvaluationResult with the appropriate state
    """
    # Determine if the measurement is dead
    if not last_seen or (now - last_seen).total_seconds() > dead_duration_seconds:
        new_state = LimitState.DEAD
    else:
        new_state = LimitState.NORMAL

    return AlertEvaluationResult(
        alert_id=alert_id,
        measurement_id=measurement_id,
        timestamp=now,
        state=new_state.name,
        limit=dead_duration_seconds,
        comparator=str(comparator_id),
        input_value=current_value if current_value is not None else 0,
        deadband=0,
        aggregate=aggregate,
        period=period,
        asset_id=asset_id,
    )
