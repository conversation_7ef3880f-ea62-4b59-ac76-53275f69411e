from datetime import datetime
from typing import Any, Dict, Optional, Union

from shared.utils.enums import LimitState

from ..base import AlertEvaluationResult, AlertEvaluator


class DeadAlertEvaluator(AlertEvaluator):
    """
    Evaluator for Dead alerts.
    Checks if a measurement has not received any data for a specified duration.
    """

    @classmethod
    def get_alert_type(cls) -> str:
        return "DEAD"

    @classmethod
    def get_required_config_params(cls) -> Dict[str, type]:
        return {"dead_duration_seconds": int, "aggregate": str, "period": str, "comparator_id": int}

    def evaluate(
        self,
        alert_id: int,
        measurement_id: int,
        asset_id: int,
        timestamp: datetime,
        input_value: Optional[Union[float, int, bool]],
        alert_config: Dict[str, Any],
        measurement_metadata: Dict[str, Any],
    ) -> Optional[AlertEvaluationResult]:
        """
        Evaluate a dead alert based on the provided data.
        """
        dead_duration_seconds = alert_config["dead_duration_seconds"]
        aggregate = alert_config["aggregate"]
        period = alert_config["period"]
        comparator_id = alert_config["comparator_id"]

        # Get the last time data was received for this measurement
        last_seen = measurement_metadata.get("last_seen")

        # Determine if the measurement is dead
        if not last_seen or (timestamp - last_seen).total_seconds() > dead_duration_seconds:
            new_state = LimitState.DEAD
        else:
            new_state = LimitState.NORMAL

        return AlertEvaluationResult(
            alert_id=alert_id,
            measurement_id=measurement_id,
            timestamp=timestamp,
            state=new_state.name,
            limit=dead_duration_seconds,
            comparator=str(comparator_id),
            input_value=input_value if input_value is not None else 0,
            deadband=0,
            aggregate=aggregate,
            period=period,
            asset_id=asset_id,
        )
