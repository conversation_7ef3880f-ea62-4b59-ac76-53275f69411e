import importlib
import inspect
import logging
import os
import pkgutil
from typing import Dict, List, Optional, Type

from ..base import AlertEvaluator

logger = logging.getLogger(__name__)


class AlertFactory:
    """
    Factory for creating and managing alert evaluators.
    This class is responsible for registering alert types and creating instances.
    """

    _evaluators: Dict[str, Type[AlertEvaluator]] = {}

    @classmethod
    def register_evaluator(cls, evaluator_class: Type[AlertEvaluator]) -> None:
        """
        Register an alert evaluator class.

        Args:
            evaluator_class: The evaluator class to register
        """
        alert_type = evaluator_class.get_alert_type()
        if alert_type in cls._evaluators:
            logger.warning(f"Alert type {alert_type} is already registered. Overwriting.")

        cls._evaluators[alert_type] = evaluator_class
        logger.info(f"Registered alert evaluator for type: {alert_type}")

    @classmethod
    def get_evaluator(cls, alert_type: str) -> Optional[AlertEvaluator]:
        """
        Get an instance of the evaluator for the specified alert type.

        Args:
            alert_type: The type of alert to get an evaluator for

        Returns:
            An instance of the appropriate evaluator, or None if not found
        """
        evaluator_class = cls._evaluators.get(alert_type)
        if evaluator_class is None:
            logger.warning(f"No evaluator registered for alert type: {alert_type}")
            return None

        return evaluator_class()

    @classmethod
    def get_registered_alert_types(cls) -> List[str]:
        """
        Get a list of all registered alert types.

        Returns:
            A list of alert type strings
        """
        return list(cls._evaluators.keys())

    @classmethod
    def initialize(cls) -> None:
        """
        Initialize the factory by discovering and registering all alert evaluators.
        This method should be called once at application startup.
        """
        # Clear existing registrations
        cls._evaluators.clear()

        # Import all evaluator modules
        try:
            # Get the path to the evaluators package
            from .. import evaluators

            evaluators_path = os.path.dirname(evaluators.__file__)

            # Find all modules in the evaluators package
            for _, module_name, is_pkg in pkgutil.iter_modules([evaluators_path]):
                if not is_pkg:
                    # Import the module
                    module = importlib.import_module(f"..evaluators.{module_name}", package=__package__)

                    # Find all classes in the module that are AlertEvaluator subclasses
                    for name, obj in inspect.getmembers(module):
                        if inspect.isclass(obj) and issubclass(obj, AlertEvaluator) and obj != AlertEvaluator:
                            # Register the evaluator
                            cls.register_evaluator(obj)

            logger.info(f"Registered {len(cls._evaluators)} alert evaluators")
        except Exception as e:
            logger.exception(f"Error initializing alert factory: {e}")
            raise
