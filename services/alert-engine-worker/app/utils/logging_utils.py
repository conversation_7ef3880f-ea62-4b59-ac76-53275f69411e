"""Logging Utilities

This module provides utilities for configuring and enhancing logging.
It leverages the BromptonPythonUtilities.Logging_Utility where possible.
"""

import logging

from celery.signals import after_setup_logger, after_setup_task_logger
from opentelemetry.trace import get_current_span
from pythonjsonlogger import jsonlogger

# Import the TraceLoggingFilter from BromptonPythonUtilities if available
try:
    from BromptonPythonUtilities.Logging_Utility.logging_config import (
        TraceLoggingFilter as BromptonTraceLoggingFilter,
    )

    # Extend the BromptonTraceLoggingFilter to add customer_id and alert_id
    class TraceLoggingFilter(BromptonTraceLoggingFilter):
        """Extended TraceLoggingFilter that adds customer and alert context."""

        def __init__(self):
            super().__init__()
            self.customer_id = None
            self.alert_id = None

        def set_context(self, customer_id=None, alert_id=None):
            """Set the customer_id and alert_id for the logging context."""
            self.customer_id = customer_id
            self.alert_id = alert_id

        def filter(self, record):
            # Call the parent filter method to add trace_id and span_id
            super().filter(record)

            # Add customer_id and alert_id
            record.customer_id = self.customer_id if self.customer_id else "unknown"
            record.alert_id = self.alert_id if self.alert_id else "unknown"
            return True

    logger = logging.getLogger(__name__)
    logger.info("Using TraceLoggingFilter from BromptonPythonUtilities")

except ImportError:
    # Fall back to our own implementation if BromptonPythonUtilities is not available
    class TraceLoggingFilter(logging.Filter):
        """
        Logging filter that adds trace context to log records.
        This includes OpenTelemetry trace and span IDs, as well as customer and alert context.
        """

        def __init__(self):
            super().__init__()
            self.customer_id = None
            self.alert_id = None

        def set_context(self, customer_id=None, alert_id=None):
            """Set the customer_id and alert_id for the logging context."""
            self.customer_id = customer_id
            self.alert_id = alert_id

        def filter(self, record):
            # Get the current span from OpenTelemetry
            span = get_current_span()
            if span and span.get_span_context():
                record.trace_id = hex(span.get_span_context().trace_id)[2:].zfill(32)
                record.span_id = hex(span.get_span_context().span_id)[2:].zfill(16)
            else:
                # Default values if no span context is available
                record.trace_id = "00000000000000000000000000000000"
                record.span_id = "0000000000000000"

            # Add customer_id and alert_id
            record.customer_id = self.customer_id if self.customer_id else "unknown"
            record.alert_id = self.alert_id if self.alert_id else "unknown"
            return True

    logger = logging.getLogger(__name__)
    logger.warning("Falling back to local TraceLoggingFilter implementation")

# Create a global instance of the filter
logging_filter = TraceLoggingFilter()


def setup_json_logging():
    """
    Set up JSON logging with trace context.
    This function configures the root logger to use JSON formatting with trace context.
    """
    handler = logging.StreamHandler()
    json_formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
    )
    handler.setFormatter(json_formatter)
    handler.addFilter(logging_filter)

    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.addHandler(handler)

    return root_logger


@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    """Configure the Celery root logger with JSON formatting and tracing."""
    handler = logging.StreamHandler()
    json_formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
    )
    handler.setFormatter(json_formatter)
    handler.addFilter(logging_filter)  # Retain tracing filter

    logger.handlers.clear()
    logger.addHandler(handler)


@after_setup_task_logger.connect
def setup_celery_task_logger(logger, *args, **kwargs):
    """Configure the Celery task logger with JSON formatting and tracing."""
    handler = logging.StreamHandler()
    json_formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(name)s %(levelname)s %(trace_id)s %(span_id)s %(customer_id)s %(alert_id)s %(message)s"
    )
    handler.setFormatter(json_formatter)
    handler.addFilter(logging_filter)  # Retain tracing filter

    logger.handlers.clear()
    logger.addHandler(handler)
