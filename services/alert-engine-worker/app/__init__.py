"""Alert Engine Worker Service

This service is responsible for evaluating alerts using the pluggable alert framework.
It processes tasks from the Celery queue and evaluates alerts based on their configurations.
"""

from .factory import AlertFactory
from .services import AlertEvaluationService

# Use relative imports for better maintainability
from .tasks.alert_tasks import evaluate_alert_task, evaluate_alert_task_async

# Initialize the alert factory
AlertFactory.initialize()

__all__ = ["evaluate_alert_task", "evaluate_alert_task_async", "AlertFactory", "AlertEvaluationService"]
