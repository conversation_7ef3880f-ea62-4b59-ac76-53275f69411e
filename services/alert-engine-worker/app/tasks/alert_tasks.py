import logging
from json import <PERSON><PERSON><PERSON>ecode<PERSON>rror
from json import loads as json_loads

from asgiref.sync import async_to_sync
from shared.alerts.models import EvaluateAlertTask
from shared.db.db_service import fetch_alerts_by_ids
from shared.utils.config import config
from shared.utils.prometheus_metrices import TASKS_CREATED
from shared.utils.redis import redis_lock

from ..celery_app import app as celery_app
from ..celery_app import set_logging_context
from ..factory import AlertFactory
from ..services import AlertEvaluationService

logger = logging.getLogger(__name__)

# Initialize the alert factory
AlertFactory.initialize()

# Configure Celery task expiration
CELERY_TASK_EXPIRES = int(config.get("celery", "task_expires", fallback=3600))


@celery_app.task(queue="evaluate_alert_task", expires=CELERY_TASK_EXPIRES)
def evaluate_alert_task(*args, **kwargs):
    """
    Celery task for evaluating alerts.
    This task uses the pluggable alert framework.
    """
    task_name, task_json = args
    task_dict = json_loads(task_json)
    customer_id = task_dict.get("customer_id", "unknown")
    alert_ids = task_dict.get("alert_ids", [])

    # Set customer and alert context for logging
    set_logging_context(customer_id=customer_id, alert_id=alert_ids[0] if alert_ids else None)

    # Log customer and alert context
    logger.info(f"Processing task for customer_id: {customer_id}, alert_ids: {alert_ids}")
    logger.info(f"Starting task {task_name}-{evaluate_alert_task.request.id}")

    TASKS_CREATED.labels(task_name=task_name, status="started").inc()
    task_lock_name = f"lock:{task_name}"

    with redis_lock(task_lock_name, expire_time=300) as lock_acquired:
        if lock_acquired:
            try:
                # Extract the task parameters from args
                task_dict = json_loads(task_json)
                logger.info(f"Task JSON: {task_dict}")

                task = EvaluateAlertTask(**task_dict)

                result = async_to_sync(evaluate_alert_task_async)(task)

                TASKS_CREATED.labels(task_name=task_name, status="completed").inc()
                return result
            except Exception as e:
                TASKS_CREATED.labels(task_name=task_name, status="failed").inc()
                logger.exception(f"Exception in task {evaluate_alert_task.name}: {str(e)}")
            finally:
                logger.info(f"Task {task_name} completed")
        else:
            TASKS_CREATED.labels(task_name=task_name, status="skipped").inc()
            logger.info(f"Task {task_name} is already running. Skipping {evaluate_alert_task.request.id}")


async def evaluate_alert_task_async(task: EvaluateAlertTask):
    """
    Asynchronous function for evaluating alerts.
    This uses the AlertEvaluationService.
    """
    try:
        customer_id = task.customer_id

        # Fetch alert configurations
        alert_configs = fetch_alerts_by_ids(task.alert_ids)

        # Set customer and alert context for logging
        set_logging_context(customer_id=task.customer_id, alert_id=task.alert_ids[0] if task.alert_ids else None)

        # Log customer and alert context
        logger.info(f"Evaluating alerts for customer_id: {task.customer_id}, alert_ids: {task.alert_ids}")

        if not alert_configs:
            logger.error(f"No alert configurations found for alert_ids: {task.alert_ids}")
            return False

        # Use the alert evaluation service
        result = await AlertEvaluationService.evaluate_alerts(
            customer_id=customer_id, alert_configs=alert_configs, aggregate=task.aggregate, period=task.aggregate_period
        )

        return result

    except JSONDecodeError as e:
        logger.exception(f"Failed to parse alert configuration: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error in evaluate_alert_task_async: {str(e)}")

    return False
