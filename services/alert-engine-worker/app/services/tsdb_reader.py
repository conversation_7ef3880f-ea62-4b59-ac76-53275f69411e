"""
TSDB Reader Service

This service provides functionality to read data from the Time Series Database.
It uses shared utilities for configuration and logging.

Note: This service is being deprecated in favor of the shared TSDB reader utility.
Consider using shared.utils.tsdb_reader.read_data_new instead.
"""

from typing import List

from shared.utils.tsdb_reader import read_data_new


# Re-export the shared function for backward compatibility
async def read_data(customer: int, measurements: List[int], aggregate=None, period=None, timeout: int = 30):
    """
    Backward compatibility wrapper for the shared TSDB reader.

    Args:
        customer (int): Customer ID
        measurements (List[int]): List of measurement IDs
        aggregate: Aggregate type
        period: Aggregate period
        timeout (int): Request timeout in seconds

    Returns:
        Dict: Response data from TSDB
    """
    return await read_data_new(customer, measurements, aggregate, period, timeout)
