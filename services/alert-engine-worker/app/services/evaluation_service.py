import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from shared.db.db_service import (
    fetch_alert_state,
    insert_event,
    insert_state_execution,
    update_alert_state,
)
from shared.db.models import Alerts
from shared.utils.enums import LimitState
from shared.utils.prometheus_metrices import (
    EVALUATE_ALERT_OUTCOMES,
    EVALUATE_ALERT_STATE_CHANGED,
)

from ..base import AlertEvaluationResult
from ..factory import AlertFactory
from .tsdb_reader import read_data_new

logger = logging.getLogger(__name__)


class AlertEvaluationService:
    """
    Service for evaluating alerts using the pluggable alert framework.
    This service handles the complete alert evaluation flow.
    """

    @staticmethod
    async def evaluate_alerts(customer_id: int, alert_configs: List[Alerts], aggregate: str, period: str) -> bool:
        """
        Evaluate a list of alerts for a customer.

        Args:
            customer_id: The ID of the customer
            alert_configs: List of alert configurations to evaluate
            aggregate: The aggregation type
            period: The aggregation period

        Returns:
            True if evaluation was successful, False otherwise
        """
        try:
            # Group alerts by measurement ID for efficient data retrieval
            measurement_to_alerts = {}
            for alert_config in alert_configs:
                meas_id = alert_config.measurement_id
                if meas_id not in measurement_to_alerts:
                    measurement_to_alerts[meas_id] = []
                measurement_to_alerts[meas_id].append(alert_config)

            # Process each measurement
            for meas_id, alerts in measurement_to_alerts.items():
                await AlertEvaluationService._evaluate_measurement_alerts(
                    customer_id, meas_id, alerts, aggregate, period
                )

            return True
        except Exception as e:
            logger.exception(f"Error evaluating alerts: {str(e)}")
            EVALUATE_ALERT_OUTCOMES.labels(outcome="failure").inc()
            return False

    @staticmethod
    async def _evaluate_measurement_alerts(
        customer_id: int, measurement_id: int, alerts: List[Alerts], aggregate: str, period: str
    ) -> None:
        """
        Evaluate all alerts for a specific measurement.

        Args:
            customer_id: The ID of the customer
            measurement_id: The ID of the measurement
            alerts: List of alert configurations for this measurement
            aggregate: The aggregation type
            period: The aggregation period
        """
        try:
            # Fetch data for this measurement
            data = await read_data_new(
                customer=customer_id, measurements=[measurement_id], aggregate=aggregate, period=period, timeout=30
            )

            # Check if we got data for this measurement
            if measurement_id not in data:
                logger.warning(f"No data found for measurement {measurement_id}")
                EVALUATE_ALERT_OUTCOMES.labels(outcome="no_data").inc()
                return

            # Get the measurement data
            meas_data = data[measurement_id]

            # Prepare measurement metadata
            measurement_metadata = {
                "status": meas_data.get("status"),
                "last_seen": datetime.fromtimestamp(meas_data.get("timestamp", 0) / 1000.0)
                if meas_data.get("timestamp")
                else None,
                "last_changed": meas_data.get("last_changed"),
            }

            # Get the current value and timestamp
            current_value = meas_data.get("value") if meas_data.get("status") == "success" else None
            timestamp = (
                datetime.fromtimestamp(meas_data.get("timestamp", 0) / 1000.0)
                if meas_data.get("timestamp")
                else datetime.now()
            )

            # Evaluate each alert for this measurement
            for alert_config in alerts:
                await AlertEvaluationService._evaluate_alert(
                    alert_config, measurement_id, current_value, timestamp, measurement_metadata
                )

        except Exception as e:
            logger.exception(f"Error evaluating measurement alerts: {str(e)}")
            EVALUATE_ALERT_OUTCOMES.labels(outcome="failure").inc()

    @staticmethod
    async def _evaluate_alert(
        alert_config: Alerts,
        measurement_id: int,
        current_value: Optional[Any],
        timestamp: datetime,
        measurement_metadata: Dict[str, Any],
    ) -> None:
        """
        Evaluate a single alert.

        Args:
            alert_config: The alert configuration
            measurement_id: The ID of the measurement
            current_value: The current value of the measurement
            timestamp: The timestamp of the current evaluation
            measurement_metadata: Additional metadata about the measurement
        """
        try:
            alert_id = alert_config.id
            asset_id = alert_config.asset_id

            # Get the appropriate evaluator for this alert type
            alert_type = alert_config.threshold_type_enum.threshold
            evaluator = AlertFactory.get_evaluator(alert_type)

            if evaluator is None:
                logger.error(f"No evaluator found for alert type {alert_type}")
                EVALUATE_ALERT_OUTCOMES.labels(outcome="failure").inc()
                return

            # Prepare alert configuration for the evaluator
            alert_params = AlertEvaluationService._prepare_alert_params(alert_config)

            # Evaluate the alert
            result = evaluator.evaluate(
                alert_id=alert_id,
                measurement_id=measurement_id,
                asset_id=asset_id,
                timestamp=timestamp,
                input_value=current_value,
                alert_config=alert_params,
                measurement_metadata=measurement_metadata,
            )

            if result is None:
                logger.debug(f"No state change for alert {alert_id}")
                EVALUATE_ALERT_OUTCOMES.labels(outcome="success").inc()
                return

            # Process the result
            await AlertEvaluationService._process_alert_result(result, alert_config)

            EVALUATE_ALERT_OUTCOMES.labels(outcome="success").inc()

        except Exception as e:
            logger.exception(f"Error evaluating alert {alert_config.id}: {str(e)}")
            EVALUATE_ALERT_OUTCOMES.labels(outcome="failure").inc()

    @staticmethod
    def _prepare_alert_params(alert_config: Alerts) -> Dict[str, Any]:
        """
        Prepare alert configuration parameters for the evaluator.

        Args:
            alert_config: The alert configuration from the database

        Returns:
            A dictionary of parameters for the evaluator
        """
        alert_type = alert_config.threshold_type_enum.threshold

        # Common parameters for all alert types
        params = {"aggregate": alert_config.agg, "period": alert_config.period, "comparator_id": alert_config.condition}

        # Add type-specific parameters
        if alert_type == "DEAD":
            params["dead_duration_seconds"] = int(alert_config.threshold_value * 60)
        elif alert_type == "STALE":
            params["stale_duration_seconds"] = int(alert_config.threshold_value * 60)
        elif alert_type in ["NOMINAL", "HIGH", "LOW"]:
            params["threshold_value"] = alert_config.threshold_value
            params["deadband"] = alert_config.reset_deadband
        elif alert_type == "ANOMALY":
            params["sensitivity"] = alert_config.threshold_value
        elif alert_type == "RATE_OF_CHANGE":
            params["rate_threshold"] = alert_config.threshold_value
            params["time_window_seconds"] = int(alert_config.reset_deadband * 60)

        return params

    @staticmethod
    async def _process_alert_result(result: AlertEvaluationResult, alert_config: Alerts) -> None:
        """
        Process the result of an alert evaluation.

        Args:
            result: The result of the evaluation
            alert_config: The alert configuration
        """
        alert_id = result.alert_id

        # Fetch the previous state from the alerts table
        prev_state = fetch_alert_state(alert_id)
        current_state = LimitState[result.state]

        logger.info(f"Alert {alert_id} state: {prev_state.name} -> {current_state.name}")

        # If the state has changed, update the database and send notifications
        if current_state.name != prev_state.name:
            # Update the state in the alert table
            update_alert_state(alert_id, current_state.name)

            # Insert the event in the events table
            insert_event(
                timestamp=result.timestamp.timestamp() * 1000,  # Convert to milliseconds
                input_value=result.input_value,
                deadband=result.deadband,
                state=current_state.name,
                limit=result.limit,
                comparator=result.comparator,
                alert_id=alert_id,
                measurement_id=result.measurement_id,
                aggregate=alert_config.agg,
                period=alert_config.period,
                asset_id=result.asset_id,
            )

            # Record state execution time
            insert_state_execution(alert_id, result.timestamp.timestamp() * 1000)

            # Send notification
            # await AlertNotificationService.send_notification(result, event_id)

            # Increment state change counter
            EVALUATE_ALERT_STATE_CHANGED.inc()
