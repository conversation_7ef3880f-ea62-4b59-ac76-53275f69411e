"""Celery Application Configuration

This module configures the Celery application for the alert-engine-worker service.
It uses shared Celery configuration utilities to ensure consistency.
"""

from BromptonPythonUtilities.Logging_Utility.logging_config import setup_logging
from shared.utils.celery_config import (
    configure_task_routes,
    create_celery_app,
)

logger = setup_logging()

# Create Celery app using shared configuration
app = create_celery_app("alert-engine-worker", include_beat_config=False)

# Configure service-specific task routes
configure_task_routes(
    app,
    {
        "app.tasks.alert_tasks.evaluate_alert_task": {"queue": "evaluate_alert_task"},
        "app.tasks.maintenance_tasks.cleanup_old_alert_states": {"queue": "maintenance_tasks"},
    },
)

# Ensure tasks are registered
app.autodiscover_tasks(["app.tasks"])

# Export the app
celery_app = app
