from datetime import datetime
from typing import Union


class AlertEvaluationResult:
    """
    Represents the result of an alert evaluation.
    This class is used to standardize the output of all alert evaluators.
    """

    def __init__(
        self,
        alert_id: int,
        measurement_id: int,
        timestamp: datetime,
        state: str,
        limit: Union[float, int, bool],
        comparator: str,
        input_value: Union[float, int, bool],
        deadband: Union[float, int, bool],
        aggregate: str,
        period: str,
        asset_id: int,
    ):
        """
        Initialize an AlertEvaluationResult.

        Args:
            alert_id: The ID of the alert
            measurement_id: The ID of the measurement
            timestamp: The timestamp of the evaluation
            state: The state of the alert (e.g., "NORMAL", "EXCEEDED")
            limit: The threshold value
            comparator: The comparison operator
            input_value: The current value of the measurement
            deadband: The deadband value
            aggregate: The aggregation type
            period: The aggregation period
            asset_id: The ID of the asset
        """
        self.alert_id = alert_id
        self.measurement_id = measurement_id
        self.timestamp = timestamp
        self.state = state
        self.limit = limit
        self.comparator = comparator
        self.input_value = input_value
        self.deadband = deadband
        self.aggregate = aggregate
        self.period = period
        self.asset_id = asset_id

    def to_dict(self) -> dict:
        """
        Convert the result to a dictionary.

        Returns:
            A dictionary representation of the result
        """
        return {
            "alert_id": self.alert_id,
            "measurement_id": self.measurement_id,
            "timestamp": self.timestamp,
            "state": self.state,
            "limit": self.limit,
            "comparator": self.comparator,
            "input_value": self.input_value,
            "deadband": self.deadband,
            "aggregate": self.aggregate,
            "period": self.period,
            "asset_id": self.asset_id,
        }
