from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional, Type, Union

from .result import AlertEvaluationResult


class AlertEvaluator(ABC):
    """
    Abstract base class for all alert evaluators.
    Any new alert type must implement this interface.
    """

    @abstractmethod
    def evaluate(
        self,
        alert_id: int,
        measurement_id: int,
        asset_id: int,
        timestamp: datetime,
        input_value: Optional[Union[float, int, bool]],
        alert_config: Dict[str, Any],
        measurement_metadata: Dict[str, Any],
    ) -> Optional[AlertEvaluationResult]:
        """
        Evaluate an alert based on the provided data.

        Args:
            alert_id: The ID of the alert being evaluated
            measurement_id: The ID of the measurement being evaluated
            asset_id: The ID of the asset being evaluated
            timestamp: The timestamp of the current evaluation
            input_value: The current value of the measurement
            alert_config: Configuration parameters for this alert
            measurement_metadata: Additional metadata about the measurement

        Returns:
            AlertEvaluationResult if a state change is detected, None otherwise
        """
        pass

    @classmethod
    @abstractmethod
    def get_alert_type(cls) -> str:
        """
        Returns the string identifier for this alert type.
        This should match the value stored in the alert_threshold_type table.
        """
        pass

    @classmethod
    def get_required_config_params(cls) -> Dict[str, Union[Type, tuple]]:
        """
        Returns a dictionary of required configuration parameters for this alert type.
        The keys are parameter names and the values are the expected types.

        Override this method if your alert type needs specific configuration.
        """
        return {}

    @classmethod
    def get_optional_config_params(cls) -> Dict[str, Union[Type, tuple]]:
        """
        Returns a dictionary of optional configuration parameters for this alert type.
        The keys are parameter names and the values are the expected types.

        Override this method if your alert type has optional configuration parameters.
        """
        return {}

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validates that the provided configuration has all required parameters
        and that they are of the correct type.

        Args:
            config: The configuration to validate

        Returns:
            True if the configuration is valid, False otherwise
        """
        # Check required parameters
        for param, param_type in self.get_required_config_params().items():
            if param not in config:
                return False

            if isinstance(param_type, tuple):
                if not any(isinstance(config[param], t) for t in param_type):
                    return False
            elif not isinstance(config[param], param_type):
                return False

        # Check optional parameters if they are provided
        for param, param_type in self.get_optional_config_params().items():
            if param in config:
                if isinstance(param_type, tuple):
                    if not any(isinstance(config[param], t) for t in param_type):
                        return False
                elif not isinstance(config[param], param_type):
                    return False

        return True
