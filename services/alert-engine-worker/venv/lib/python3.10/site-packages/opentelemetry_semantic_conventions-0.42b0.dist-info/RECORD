opentelemetry/semconv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opentelemetry/semconv/__pycache__/__init__.cpython-310.pyc,,
opentelemetry/semconv/__pycache__/version.cpython-310.pyc,,
opentelemetry/semconv/metrics/__init__.py,sha256=fM64LeRVYqSJOTVQpiFKWzlTmaO0gNKH1rBQ1SUVzS4,5568
opentelemetry/semconv/metrics/__pycache__/__init__.cpython-310.pyc,,
opentelemetry/semconv/resource/__init__.py,sha256=A15_GkfzDlyg9DL8uMlK-jNY_B_EzL0MFJWyYkP6Wqs,31873
opentelemetry/semconv/resource/__pycache__/__init__.cpython-310.pyc,,
opentelemetry/semconv/trace/__init__.py,sha256=80wVl-WG6g59g0efF2pD7F_pP5RPqGCRg12qBEBZAuA,68305
opentelemetry/semconv/trace/__pycache__/__init__.cpython-310.pyc,,
opentelemetry/semconv/version.py,sha256=Qmz6PKJoEeD8yuxjT2ts28U3I0PoIptGyb5gXaFDP-I,608
opentelemetry_semantic_conventions-0.42b0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
opentelemetry_semantic_conventions-0.42b0.dist-info/METADATA,sha256=OUGUCoNLMbFiJnpa4mGsP30FDWxDh_d47QHyHkJbde8,2288
opentelemetry_semantic_conventions-0.42b0.dist-info/RECORD,,
opentelemetry_semantic_conventions-0.42b0.dist-info/WHEEL,sha256=KGYbc1zXlYddvwxnNty23BeaKzh7YuoSIvIMO4jEhvw,87
opentelemetry_semantic_conventions-0.42b0.dist-info/licenses/LICENSE,sha256=h8jwqxShIeVkc8vOo9ynxGYW16f4fVPxLhZKZs0H5U8,11350
