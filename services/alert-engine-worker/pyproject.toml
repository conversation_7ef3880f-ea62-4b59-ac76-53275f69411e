[tool.black]
# Black code formatter configuration
line-length = 120
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
# isort import sorting configuration
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["app", "shared"]
known_third_party = ["celery", "redis", "pydantic", "pytest"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.pylint.messages_control]
# Pylint configuration
disable = [
    "C0114",  # missing-module-docstring
    "C0115",  # missing-class-docstring  
    "C0116",  # missing-function-docstring
    "R0903",  # too-few-public-methods
    "E0401",  # import-error (for shared modules)
]

[tool.pylint.format]
max-line-length = 120

[tool.pylint.design]
max-args = 7
max-locals = 15
max-returns = 6
max-branches = 12
max-statements = 50

[tool.pytest.ini_options]
# Pytest configuration
testpaths = ["tests", "unit_tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.mypy]
# MyPy type checking configuration
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "shared.*",
    "BromptonPythonUtilities.*",
    "testcontainers.*",
]
ignore_missing_imports = true
