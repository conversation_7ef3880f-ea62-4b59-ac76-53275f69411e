#!/usr/bin/env python3
"""
Import Validation Test Script

This script validates that all import statements across the three microservices
are correctly structured and can be resolved without errors.

Run from repository root: python test_imports.py
"""

import sys
import os
import ast
import importlib.util
from pathlib import Path

def test_syntax_validation():
    """Test that all Python files have valid syntax."""
    print("🔍 Testing Python syntax validation...")
    
    files_to_test = [
        "services/scheduler-api/app/main.py",
        "services/scheduler-api/app/celery_app.py", 
        "services/scheduler-api/app/scheduler_service.py",
        "services/scheduler-api/app/middleware.py",
        "services/alert-engine-worker/app/celery_app.py",
        "services/alert-engine-worker/app/tasks/alert_tasks.py",
        "services/alert-engine-worker/app/services/evaluation_service.py",
        "services/celery-beat-engine/app/celery_app.py",
    ]
    
    for file_path in files_to_test:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            ast.parse(content)
            print(f"  ✅ {file_path}")
        except SyntaxError as e:
            print(f"  ❌ {file_path}: Syntax Error - {e}")
            return False
        except FileNotFoundError:
            print(f"  ⚠️  {file_path}: File not found")
            return False
    
    return True

def test_import_statements():
    """Test that import statements are correctly structured."""
    print("\n🔍 Testing import statement structure...")
    
    # Test scheduler-api imports
    scheduler_main_imports = [
        "from .celery_app import app as celery_app",
        "from .scheduler_service import delete_task, create_task", 
        "from .middleware import CustomMiddleware",
        "from shared.db.db_service import fetch_alerts_by_customer_id",
        "from shared.utils.enums import Aggregate, AggregatePeriod",
        "from shared.alerts.models import EvaluateAlertTask, CreateAlertTask, UpdateAlertTask, DeleteAlertTask, AlertConfig",
        "from shared.utils.tsdb_reader import read_data",
    ]
    
    # Test alert-engine-worker imports
    alert_worker_imports = [
        "from ..celery_app import app as celery_app",
        "from ..factory import AlertFactory",
        "from ..services import AlertEvaluationService", 
        "from shared.alerts.models import EvaluateAlertTask",
        "from shared.utils.celery_config import set_logging_context",
    ]
    
    # Test celery-beat-engine imports
    beat_engine_imports = [
        "from celery import Celery",
        "from kombu import Queue",
        "from shared.utils.celery_config import create_celery_app, configure_task_routes",
        "from shared.utils.config import config",
    ]
    
    def check_imports_in_file(file_path, expected_imports):
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            found_imports = []
            missing_imports = []
            
            for expected_import in expected_imports:
                if expected_import in content:
                    found_imports.append(expected_import)
                else:
                    missing_imports.append(expected_import)
            
            if missing_imports:
                print(f"  ❌ {file_path}: Missing imports:")
                for imp in missing_imports:
                    print(f"    - {imp}")
                return False
            else:
                print(f"  ✅ {file_path}: All expected imports found")
                return True
                
        except FileNotFoundError:
            print(f"  ⚠️  {file_path}: File not found")
            return False
    
    results = []
    results.append(check_imports_in_file("services/scheduler-api/app/main.py", scheduler_main_imports))
    results.append(check_imports_in_file("services/alert-engine-worker/app/tasks/alert_tasks.py", alert_worker_imports))
    results.append(check_imports_in_file("services/celery-beat-engine/app/celery_app.py", beat_engine_imports))
    
    return all(results)

def test_no_cross_service_imports():
    """Test that there are no cross-service imports."""
    print("\n🔍 Testing for cross-service imports...")
    
    prohibited_patterns = [
        ("services/scheduler-api/", "services.alert_engine_worker"),
        ("services/scheduler-api/", "services.celery_beat_engine"),
        ("services/alert-engine-worker/", "services.scheduler_api"),
        ("services/alert-engine-worker/", "services.celery_beat_engine"),
        ("services/celery-beat-engine/", "services.scheduler_api"),
        ("services/celery-beat-engine/", "services.alert_engine_worker"),
    ]
    
    all_good = True
    
    for service_dir, prohibited_import in prohibited_patterns:
        for py_file in Path(service_dir).rglob("*.py"):
            if "venv" in str(py_file):  # Skip virtual environment files
                continue
                
            try:
                with open(py_file, 'r') as f:
                    content = f.read()
                
                if prohibited_import in content:
                    print(f"  ❌ {py_file}: Found prohibited cross-service import: {prohibited_import}")
                    all_good = False
            except Exception:
                continue
    
    if all_good:
        print("  ✅ No cross-service imports found")
    
    return all_good

def test_relative_imports():
    """Test that relative imports are used correctly within services."""
    print("\n🔍 Testing relative import usage...")
    
    # Check that services use relative imports for internal modules
    relative_import_patterns = [
        ("services/scheduler-api/app/main.py", "from .celery_app import"),
        ("services/scheduler-api/app/main.py", "from .scheduler_service import"),
        ("services/scheduler-api/app/main.py", "from .middleware import"),
        ("services/scheduler-api/app/scheduler_service.py", "from .celery_app import"),
        ("services/alert-engine-worker/app/tasks/alert_tasks.py", "from ..celery_app import"),
        ("services/alert-engine-worker/app/tasks/alert_tasks.py", "from ..factory import"),
        ("services/alert-engine-worker/app/tasks/alert_tasks.py", "from ..services import"),
    ]
    
    all_good = True
    
    for file_path, expected_pattern in relative_import_patterns:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            if expected_pattern in content:
                print(f"  ✅ {file_path}: Found expected relative import pattern")
            else:
                print(f"  ❌ {file_path}: Missing expected relative import: {expected_pattern}")
                all_good = False
                
        except FileNotFoundError:
            print(f"  ⚠️  {file_path}: File not found")
            all_good = False
    
    return all_good

def test_shared_imports():
    """Test that shared components are imported correctly."""
    print("\n🔍 Testing shared component imports...")
    
    shared_import_patterns = [
        ("services/scheduler-api/app/main.py", "from shared.db.db_service import"),
        ("services/scheduler-api/app/main.py", "from shared.utils.enums import"),
        ("services/scheduler-api/app/main.py", "from shared.alerts.models import"),
        ("services/alert-engine-worker/app/tasks/alert_tasks.py", "from shared.alerts.models import"),
        ("services/alert-engine-worker/app/tasks/alert_tasks.py", "from shared.utils.celery_config import"),
        ("services/celery-beat-engine/app/celery_app.py", "from shared.utils.celery_config import"),
        ("services/celery-beat-engine/app/celery_app.py", "from shared.utils.config import"),
    ]
    
    all_good = True
    
    for file_path, expected_pattern in shared_import_patterns:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            if expected_pattern in content:
                print(f"  ✅ {file_path}: Found expected shared import pattern")
            else:
                print(f"  ❌ {file_path}: Missing expected shared import: {expected_pattern}")
                all_good = False
                
        except FileNotFoundError:
            print(f"  ⚠️  {file_path}: File not found")
            all_good = False
    
    return all_good

def main():
    """Run all import validation tests."""
    print("🚀 Starting Import Validation Tests\n")
    
    # Add current directory to Python path
    sys.path.insert(0, '.')
    
    tests = [
        ("Syntax Validation", test_syntax_validation),
        ("Import Statement Structure", test_import_statements),
        ("Cross-Service Import Check", test_no_cross_service_imports),
        ("Relative Import Usage", test_relative_imports),
        ("Shared Component Imports", test_shared_imports),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("IMPORT VALIDATION SUMMARY")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All import validation tests PASSED!")
        print("✅ Import structure is correctly configured for microservice architecture")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) FAILED")
        print("❌ Import structure needs attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
